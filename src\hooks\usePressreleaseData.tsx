import { useState, useCallback, useEffect, useRef } from "preact/hooks";
import { useQuery } from "urql";
import { PRESS_RELEASES_BY_INSTRUMENT_ID_QUERY } from "../services/graphql/queries";

interface PressRelease {
  node: {
    title: string;
    dateTime: string;
    __typename: string;
  };
  cursor: string;
  __typename: string;
}

export const usePressreleaseData = (instrumentIds: number) => {
  const [allData, setAllData] = useState<PressRelease[]>([]);
  const [endCursor, setEndCursor] = useState<string | null>(null);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const loadingRef = useRef(false); // Prevent multiple concurrent loads

  const [result, executeQuery] = useQuery({
    query: PRESS_RELEASES_BY_INSTRUMENT_ID_QUERY,
    variables: {
      id: instrumentIds,
      first: 10,
      after: null,
    },
  });

  const { data: pressReleaseDatas, fetching } = result;

  // Initialize data on first load using useEffect
  useEffect(() => {
    if (pressReleaseDatas && !fetching) {
      const edges = pressReleaseDatas?.instrumentById?.company?.pressReleases?.edges || [];
      const pageInfo = pressReleaseDatas?.instrumentById?.company?.pressReleases?.pageInfo;

      console.log('Initial data loaded:', edges.length, 'items');
      console.log('Page info:', pageInfo);

      setAllData(edges);
      setEndCursor(pageInfo?.endCursor || null);
      setHasNextPage(pageInfo?.hasNextPage || false);
    }
  }, [pressReleaseDatas, fetching]);

  const loadMore = useCallback(async () => {
    console.log('loadMore called:', { hasNextPage, isLoadingMore, endCursor, loadingRef: loadingRef.current });

    // Prevent multiple concurrent loads
    if (!hasNextPage || isLoadingMore || !endCursor || loadingRef.current) {
      console.log('loadMore blocked:', { hasNextPage, isLoadingMore, endCursor, loadingRef: loadingRef.current });
      return;
    }

    console.log('Loading more data with cursor:', endCursor);
    setIsLoadingMore(true);
    loadingRef.current = true;

    try {
      const result = await executeQuery({
        id: instrumentIds,
        first: 10,
        after: endCursor,
      });

      console.log('Load more result:', result);

      if (result.data) {
        const newEdges = result.data?.instrumentById?.company?.pressReleases?.edges || [];
        const pageInfo = result.data?.instrumentById?.company?.pressReleases?.pageInfo;

        console.log('New edges loaded:', newEdges.length);
        console.log('New page info:', pageInfo);

        // Check for duplicates before adding
        setAllData(prev => {
          const existingCursors = new Set(prev.map(item => item.cursor));
          const filteredNewEdges = newEdges.filter(edge => !existingCursors.has(edge.cursor));
          const updated = [...prev, ...filteredNewEdges];
          console.log('Total items after load more:', updated.length);
          return updated;
        });

        setEndCursor(pageInfo?.endCursor || null);
        setHasNextPage(pageInfo?.hasNextPage || false);
      }
    } catch (error) {
      console.error('Error loading more data:', error);
    } finally {
      setIsLoadingMore(false);
      loadingRef.current = false;
    }
  }, [hasNextPage, isLoadingMore, endCursor, instrumentIds, executeQuery]);

  return {
    data: allData,
    pageInfo: { hasNextPage, endCursor },
    loading: fetching && allData.length === 0,
    loadingMore: isLoadingMore,
    loadMore
  };
};